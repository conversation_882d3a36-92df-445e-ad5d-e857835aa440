import { PrismaClient, ServiceCategorySlug, PendingServiceStatus } from '@prisma/client';
import { FileUploadService } from './file-upload-service';

const prisma = new PrismaClient();

export interface ServiceSubmissionData {
  categoryId: number;
  serviceCategorySlug: ServiceCategorySlug;
  serviceName?: string;
  description: string;
  experienceYears: number;
  
  // File uploads
  DocBuletinFile?: File | null;
  DocDiplomeFiles?: File[] | null;
  DocRecomandariFiles?: File[] | null;
  
  // Service-specific details (will be distributed to appropriate detail models)
  [key: string]: any;
}

export interface ProviderRegistrationData {
  userId: number;
  userName: string;
  userEmail: string;
  services: ServiceSubmissionData[];
}

export interface ServiceCreationResult {
  success: boolean;
  serviceId?: number;
  error?: string;
}

export interface RegistrationResult {
  success: boolean;
  requestId?: string;
  servicesCreated?: number;
  errors?: string[];
}

/**
 * Service for handling granular provider registration
 */
export class ProviderRegistrationService {
  
  /**
   * Submit a new provider registration with multiple services
   */
  static async submitRegistration(data: ProviderRegistrationData): Promise<RegistrationResult> {
    const transaction = await prisma.$transaction(async (tx) => {
      try {
        // 1. Check for existing request
        const existingRequest = await tx.providerRegistrationRequest.findUnique({
          where: { UserId: data.userId }
        });

        let request;
        
        if (existingRequest) {
          if (existingRequest.Status === 'Pending' || existingRequest.Status === 'Approved') {
            throw new Error(`Ai deja o cerere cu statusul: ${existingRequest.Status}`);
          }
          
          // Update existing rejected request
          request = await tx.providerRegistrationRequest.update({
            where: { Id: existingRequest.Id },
            data: {
              UserName: data.userName,
              UserEmail: data.userEmail,
              Status: 'Pending',
              RequestDate: new Date(),
              AdminNotes: null,
            }
          });
          
          // Delete old pending services for resubmission
          await tx.pendingService.deleteMany({
            where: { RequestId: existingRequest.Id }
          });
        } else {
          // Create new request
          request = await tx.providerRegistrationRequest.create({
            data: {
              UserId: data.userId,
              UserName: data.userName,
              UserEmail: data.userEmail,
              Status: 'Pending',
              RequestDate: new Date(),
            }
          });
        }

        // 2. Create pending services
        const serviceResults: ServiceCreationResult[] = [];
        
        for (const serviceData of data.services) {
          try {
            const result = await this.createPendingService(tx, request.Id, serviceData);
            serviceResults.push(result);
          } catch (error) {
            serviceResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const successfulServices = serviceResults.filter(r => r.success);
        const failedServices = serviceResults.filter(r => !r.success);

        if (failedServices.length > 0) {
          throw new Error(`Failed to create ${failedServices.length} services: ${failedServices.map(f => f.error).join(', ')}`);
        }

        return {
          success: true,
          requestId: request.Id,
          servicesCreated: successfulServices.length,
        };

      } catch (error) {
        throw error;
      }
    });

    return transaction;
  }

  /**
   * Create a single pending service with its details
   */
  private static async createPendingService(
    tx: any,
    requestId: string,
    serviceData: ServiceSubmissionData
  ): Promise<ServiceCreationResult> {
    try {
      // 1. Process file uploads
      const documentPaths: string[] = [];
      
      // Simulate file upload processing (in real implementation, files would be uploaded here)
      if (serviceData.DocBuletinFile) {
        const result = await FileUploadService.simulateFileUpload(
          serviceData.DocBuletinFile.name,
          parseInt(requestId.slice(-6), 16), // Use part of requestId as userId simulation
          serviceData.serviceCategorySlug
        );
        if (result.success && result.filePath) {
          documentPaths.push(result.filePath);
        }
      }

      if (serviceData.DocDiplomeFiles && serviceData.DocDiplomeFiles.length > 0) {
        for (const file of serviceData.DocDiplomeFiles) {
          const result = await FileUploadService.simulateFileUpload(
            file.name,
            parseInt(requestId.slice(-6), 16),
            serviceData.serviceCategorySlug
          );
          if (result.success && result.filePath) {
            documentPaths.push(result.filePath);
          }
        }
      }

      if (serviceData.DocRecomandariFiles && serviceData.DocRecomandariFiles.length > 0) {
        for (const file of serviceData.DocRecomandariFiles) {
          const result = await FileUploadService.simulateFileUpload(
            file.name,
            parseInt(requestId.slice(-6), 16),
            serviceData.serviceCategorySlug
          );
          if (result.success && result.filePath) {
            documentPaths.push(result.filePath);
          }
        }
      }

      // 2. Create the pending service
      const serviceName = serviceData.serviceName || `Serviciu de ${serviceData.serviceCategorySlug}`;
      
      const pendingService = await tx.pendingService.create({
        data: {
          RequestId: requestId,
          CategoryId: serviceData.categoryId,
          ServiceCategorySlug: serviceData.serviceCategorySlug,
          ServiceName: serviceName,
          Description: serviceData.description,
          ExperienceYears: serviceData.experienceYears,
          Status: 'PendingReview',
          DocumentPaths: documentPaths,
        }
      });

      // 3. Create service-specific details
      await this.createServiceDetails(tx, pendingService.Id, serviceData);

      return {
        success: true,
        serviceId: pendingService.Id,
      };

    } catch (error) {
      console.error('Error creating pending service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create service-specific detail records
   */
  private static async createServiceDetails(
    tx: any,
    pendingServiceId: number,
    serviceData: ServiceSubmissionData
  ): Promise<void> {
    const slug = serviceData.serviceCategorySlug;

    switch (slug) {
      case 'Nanny':
        await tx.pendingNannyServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            PreferredAge_0_2: serviceData.PreferredAge_0_2 || false,
            PreferredAge_3_6: serviceData.PreferredAge_3_6 || false,
            PreferredAge_7_plus: serviceData.PreferredAge_7_plus || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceBabysitting: serviceData.ServiceBabysitting || false,
            ServicePlaytime: serviceData.ServicePlaytime || false,
            ServiceMeals: serviceData.ServiceMeals || false,
            ServiceBedtime: serviceData.ServiceBedtime || false,
            ServiceEducational: serviceData.ServiceEducational || false,
            ServiceOutdoor: serviceData.ServiceOutdoor || false,
            ServiceTransport: serviceData.ServiceTransport || false,
            ServiceHousework: serviceData.ServiceHousework || false,
            ExtraFirstAid: serviceData.ExtraFirstAid || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraCooking: serviceData.ExtraCooking || false,
            ExtraLanguages: serviceData.ExtraLanguages || null,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
            ExtraOvernightCare: serviceData.ExtraOvernightCare || false,
          }
        });
        break;

      case 'Cleaning':
        await tx.pendingCleaningServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceRegularCleaning: serviceData.ServiceRegularCleaning || false,
            ServiceDeepCleaning: serviceData.ServiceDeepCleaning || false,
            ServiceWindowCleaning: serviceData.ServiceWindowCleaning || false,
            ServiceCarpetCleaning: serviceData.ServiceCarpetCleaning || false,
            ServiceLaundry: serviceData.ServiceLaundry || false,
            ServiceIroning: serviceData.ServiceIroning || false,
            ServiceOrganizing: serviceData.ServiceOrganizing || false,
            ServicePostConstruction: serviceData.ServicePostConstruction || false,
            ExtraOwnSupplies: serviceData.ExtraOwnSupplies || false,
            ExtraEcoFriendly: serviceData.ExtraEcoFriendly || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraInsured: serviceData.ExtraInsured || false,
            ExtraWeekendAvailable: serviceData.ExtraWeekendAvailable || false,
            ExtraEmergencyService: serviceData.ExtraEmergencyService || false,
          }
        });
        break;

      case 'ElderCare':
        await tx.pendingElderCareServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServicePersonalCare: serviceData.ServicePersonalCare || false,
            ServiceMedicalCare: serviceData.ServiceMedicalCare || false,
            ServiceMobility: serviceData.ServiceMobility || false,
            ServiceCompanionship: serviceData.ServiceCompanionship || false,
            ServiceMeals: serviceData.ServiceMeals || false,
            ServiceHousework: serviceData.ServiceHousework || false,
            ServiceShopping: serviceData.ServiceShopping || false,
            ServiceTransport: serviceData.ServiceTransport || false,
            ExtraFirstAid: serviceData.ExtraFirstAid || false,
            ExtraOwnTransport: serviceData.ExtraOwnTransport || false,
            ExtraCooking: serviceData.ExtraCooking || false,
            ExtraLanguages: serviceData.ExtraLanguages || null,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
            ExtraOvernightCare: serviceData.ExtraOvernightCare || false,
          }
        });
        break;

      case 'Tutoring':
        await tx.pendingTutoringServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            SubjectMath: serviceData.SubjectMath || false,
            SubjectScience: serviceData.SubjectScience || false,
            SubjectLanguages: serviceData.SubjectLanguages || false,
            SubjectHistory: serviceData.SubjectHistory || false,
            SubjectArts: serviceData.SubjectArts || false,
            SubjectMusic: serviceData.SubjectMusic || false,
            SubjectSports: serviceData.SubjectSports || false,
            SubjectComputer: serviceData.SubjectComputer || false,
            LevelPreschool: serviceData.LevelPreschool || false,
            LevelPrimary: serviceData.LevelPrimary || false,
            LevelSecondary: serviceData.LevelSecondary || false,
            LevelHighSchool: serviceData.LevelHighSchool || false,
            LevelUniversity: serviceData.LevelUniversity || false,
            ExtraOnlineTeaching: serviceData.ExtraOnlineTeaching || false,
            ExtraGroupLessons: serviceData.ExtraGroupLessons || false,
            ExtraHomeworkHelp: serviceData.ExtraHomeworkHelp || false,
            ExtraExamPrep: serviceData.ExtraExamPrep || false,
            ExtraSpecialNeeds: serviceData.ExtraSpecialNeeds || false,
          }
        });
        break;

      case 'Cooking':
        await tx.pendingCookingServiceDetails.create({
          data: {
            PendingServiceId: pendingServiceId,
            LocationId: serviceData.LocationId || null,
            PricePerHour: serviceData.PricePerHour || null,
            PricePerDay: serviceData.PricePerDay || null,
            AvailabilityWeekdays: serviceData.availabilityWeekdays || false,
            AvailabilityWeekends: serviceData.availabilityWeekends || false,
            AvailabilityEvenings: serviceData.availabilityEvenings || false,
            AvailabilityFullTime: serviceData.AvailabilityFullTime || false,
            AvailabilityPartTime: serviceData.AvailabilityPartTime || false,
            AvailabilityOccasional: serviceData.AvailabilityOccasional || false,
            ServiceMealPrep: serviceData.ServiceMealPrep || false,
            ServiceCooking: serviceData.ServiceCooking || false,
            ServiceBaking: serviceData.ServiceBaking || false,
            ServiceSpecialDiet: serviceData.ServiceSpecialDiet || false,
            ServiceEventCatering: serviceData.ServiceEventCatering || false,
            ServiceMealPlanning: serviceData.ServiceMealPlanning || false,
            ServiceGroceryShopping: serviceData.ServiceGroceryShopping || false,
            ServiceKitchenCleaning: serviceData.ServiceKitchenCleaning || false,
            CuisineRomanian: serviceData.CuisineRomanian || false,
            CuisineItalian: serviceData.CuisineItalian || false,
            CuisineAsian: serviceData.CuisineAsian || false,
            CuisineMediterranean: serviceData.CuisineMediterranean || false,
            CuisineVegetarian: serviceData.CuisineVegetarian || false,
            CuisineVegan: serviceData.CuisineVegan || false,
            ExtraOwnIngredients: serviceData.ExtraOwnIngredients || false,
            ExtraSpecialDiet: serviceData.ExtraSpecialDiet || false,
            ExtraLargeGroups: serviceData.ExtraLargeGroups || false,
            ExtraWeekendService: serviceData.ExtraWeekendService || false,
          }
        });
        break;

      default:
        console.warn(`Service details creation not implemented for: ${slug}`);
    }
  }

  /**
   * Get pending services for a request
   */
  static async getPendingServices(requestId: string) {
    return await prisma.pendingService.findMany({
      where: { RequestId: requestId },
      include: {
        Category: true,
        NannyServiceDetails: true,
        ElderCareServiceDetails: true,
        CleaningServiceDetails: true,
        TutoringServiceDetails: true,
        CookingServiceDetails: true,
      }
    });
  }

  /**
   * Copy a pending service to AdvertisedService with the specified status
   */
  static async copyPendingServiceToLive(
    tx: any,
    pendingService: any,
    targetStatus: 'Approved' | 'Rejected'
  ) {
    console.log(`[Service Copy] Copying service ${pendingService.ServiceName} with status ${targetStatus}`);

    // Create the AdvertisedService
    const advertisedService = await tx.advertisedService.create({
      data: {
        ProviderId: pendingService.Request.UserId,
        CategoryId: pendingService.CategoryId,
        ServiceCategorySlug: pendingService.ServiceCategorySlug,
        ServiceName: pendingService.ServiceName,
        Description: pendingService.Description,
        Status: targetStatus === 'Approved' ? 'Activ' : 'Rejected',
      }
    });

    console.log(`[Service Copy] Created AdvertisedService with ID: ${advertisedService.Id}`);

    // Copy service-specific details based on category
    await this.copyServiceDetails(tx, pendingService, advertisedService.Id);

    return advertisedService;
  }

  /**
   * Copy service-specific details from pending to live tables
   */
  private static async copyServiceDetails(tx: any, pendingService: any, advertisedServiceId: number) {
    const baseData = {
      AdvertisedServiceId: advertisedServiceId,
      ExperienceYears: pendingService.ExperienceYears,
    };

    switch (pendingService.ServiceCategorySlug) {
      case 'Cleaning':
        if (pendingService.CleaningServiceDetails) {
          const details = pendingService.CleaningServiceDetails;
          await tx.cleaningServiceDetails.create({
            data: {
              ...baseData,
              LocationId: details.LocationId,
              PricePerHour: details.PricePerHour ? parseFloat(details.PricePerHour) : null,
              PricePerDay: details.PricePerDay ? parseFloat(details.PricePerDay) : null,
              AvailabilityWeekdays: details.AvailabilityWeekdays,
              AvailabilityWeekends: details.AvailabilityWeekends,
              AvailabilityEvenings: details.AvailabilityEvenings,
              PropertyTypeApartments: true, // Default mapping - could be enhanced
              PropertyTypeHouses: true,
              PropertyTypeOffices: false,
              OwnProducts: details.ExtraOwnSupplies,
              TypeGeneral: details.ServiceRegularCleaning,
              TypePostRenovation: details.ServicePostConstruction,
              TypeOccasional: details.AvailabilityOccasional,
              TypeRegular: details.ServiceRegularCleaning,
              ExtraIroning: details.ServiceIroning,
              ExtraWindows: details.ServiceWindowCleaning,
              ExtraDisinfection: details.ExtraEcoFriendly,
              DocBuletinFileName: null, // These would need to be handled separately
              DocDiplomeFileNames: [],
              DocRecomandariFileNames: [],
            }
          });
        }
        break;

      case 'Cooking':
        if (pendingService.CookingServiceDetails) {
          const details = pendingService.CookingServiceDetails;
          await tx.cookingServiceDetails.create({
            data: {
              ...baseData,
              LocationId: details.LocationId,
              PricePerHour: details.PricePerHour ? parseFloat(details.PricePerHour) : null,
              PricePerDay: details.PricePerDay ? parseFloat(details.PricePerDay) : null,
              AvailabilityWeekdays: details.AvailabilityWeekdays,
              AvailabilityWeekends: details.AvailabilityWeekends,
              AvailabilityEvenings: details.AvailabilityEvenings,
              CuisineTypeTraditional: details.CuisineRomanian,
              CuisineTypeVegetarian: details.CuisineVegetarian,
              CuisineTypeKids: false, // No direct mapping
              CuisineTypeDiet: details.ServiceSpecialDiet,
              OffersDelivery: false, // No direct mapping
              AtClientHome: true, // Default assumption
              AtOwnHome: false,
              cookingOwnProducts: details.ExtraOwnIngredients,
              MinPortions: details.MinPortions ? parseInt(details.MinPortions) : null,
              WeeklySubscription: details.ExtraWeekendAvailable,
              PricePerMeal: details.PricePerMeal ? parseFloat(details.PricePerMeal) : null,
              MealDetails: details.SubscriptionDetails,
              DocBuletinFileName: null,
              DocDiplomeFileNames: [],
              DocRecomandariFileNames: [],
            }
          });
        }
        break;

      case 'ElderCare':
        if (pendingService.ElderCareServiceDetails) {
          const details = pendingService.ElderCareServiceDetails;
          await tx.elderCareServiceDetails.create({
            data: {
              ...baseData,
              LocationId: details.LocationId,
              PricePerHour: details.PricePerHour ? parseFloat(details.PricePerHour) : null,
              PricePerDay: details.PricePerDay ? parseFloat(details.PricePerDay) : null,
              AvailabilityWeekdays: details.AvailabilityWeekdays,
              AvailabilityWeekends: details.AvailabilityWeekends,
              AvailabilityEvenings: details.AvailabilityEvenings,
              TypeMobil: details.ServiceMobility,
              TypePartialImobilizat: details.ServicePersonalCare,
              TypeCompletImobilizat: details.ServicePersonalCare,
              MedicalKnowledgeBasic: details.ExtraFirstAid,
              MedicalKnowledgeAdvanced: details.ExtraMedicalTraining,
              MedicationAdmin: details.ServiceMedicalSupport,
              DrivingLicense: details.ExtraOwnTransport,
              ActivityCooking: details.ServiceMeals,
              ActivityCleaningLight: details.ServiceHousekeeping,
              ActivityCompanionship: details.ServiceCompanionship,
              DocBuletinFileName: null,
              DocDiplomeFileNames: [],
              DocRecomandariFileNames: [],
            }
          });
        }
        break;

      case 'Nanny':
        if (pendingService.NannyServiceDetails) {
          const details = pendingService.NannyServiceDetails;
          console.log(`[Service Copy Debug] Nanny service details:`, JSON.stringify(details, null, 2));
          await tx.nannyServiceDetails.create({
            data: {
              ...baseData,
              LocationId: details.LocationId,
              PricePerHour: details.PricePerHour ? parseFloat(details.PricePerHour) : null,
              PricePerDay: details.PricePerDay ? parseFloat(details.PricePerDay) : null,
              AvailabilityWeekdays: details.AvailabilityWeekdays || false,
              AvailabilityWeekends: details.AvailabilityWeekends || false,
              AvailabilityEvenings: details.AvailabilityEvenings || false,
              PreferredAge_0_2: details.PreferredAge_0_2 || false,
              PreferredAge_3_6: details.PreferredAge_3_6 || false,
              PreferredAge_7_plus: details.PreferredAge_7_plus || false,
              AvailabilityFullTime: details.AvailabilityFullTime || false,
              AvailabilityPartTime: details.AvailabilityPartTime || false,
              FirstAid: details.ExtraFirstAid || false,
              SchoolPickup: details.ServiceTransport || false,
              ActivityWalks: details.ServiceOutdoor || false,
              ActivityGames: details.ServicePlaytime || false,
              ActivityFeeding: details.ServiceMeals || false,
              ActivitySleep: details.ServiceBedtime || false,
              DocBuletinFileName: null,
              DocDiplomeFileNames: [],
              DocRecomandariFileNames: [],
            }
          });
        }
        break;

      case 'Tutoring':
        if (pendingService.TutoringServiceDetails) {
          const details = pendingService.TutoringServiceDetails;
          await tx.tutoringServiceDetails.create({
            data: {
              ...baseData,
              LocationId: details.LocationId,
              PricePerHour: details.PricePerHour ? parseFloat(details.PricePerHour) : null,
              PricePerDay: details.PricePerDay ? parseFloat(details.PricePerDay) : null,
              AvailabilityWeekdays: details.AvailabilityWeekdays,
              AvailabilityWeekends: details.AvailabilityWeekends,
              AvailabilityEvenings: details.AvailabilityEvenings,
              ServiceAfterSchool: details.ServiceAfterSchool,
              ServiceHomeworkHelp: details.ServiceHomeworkHelp,
              ServiceIndividualLessons: details.ServiceIndividualLessons,
              Grades_1_4: details.Grades_1_4,
              Grades_5_8: details.Grades_5_8,
              Grades_9_12: details.Grades_9_12,
              SubjectRomanian: details.SubjectRomanian,
              SubjectMath: details.SubjectMath,
              SubjectEnglish: details.SubjectEnglish,
              SubjectOther: details.SubjectOther,
              FormatOnline: details.FormatOnline,
              FormatOwnHome: details.FormatOwnHome,
              FormatChildHome: details.FormatChildHome,
              ExtraGames: details.ExtraGames,
              ExtraSnack: details.ExtraSnack,
              ExtraTransport: details.ExtraTransport,
              ExtraSupervisedHomework: details.ExtraSupervisedHomework,
              DocBuletinFileName: null,
              DocDiplomeFileNames: [],
              DocRecomandariFileNames: [],
            }
          });
        }
        break;

      default:
        console.warn(`[Service Copy] Unknown service category: ${pendingService.ServiceCategorySlug}`);
    }

    console.log(`[Service Copy] Successfully copied details for ${pendingService.ServiceCategorySlug} service`);
  }

  /**
   * Calculate the overall request status based on individual service statuses
   */
  static calculateRequestStatus(services: Array<{ Status: string }>): 'Approved' | 'Rejected' | 'Pending' {
    const statuses = services.map(s => s.Status);

    // If all services are approved, request is approved
    if (statuses.every(status => status === 'Approved')) {
      return 'Approved';
    }

    // If all services are rejected, request is rejected
    if (statuses.every(status => status === 'Rejected')) {
      return 'Rejected';
    }

    // Otherwise, request is pending (mixed statuses or requires changes)
    return 'Pending';
  }

  /**
   * Check if provider role should be granted (at least one approved service)
   */
  static shouldGrantProviderRole(services: Array<{ Status: string }>): boolean {
    return services.some(service => service.Status === 'Approved');
  }

  /**
   * Grant provider role to user if not already assigned
   */
  static async grantProviderRole(tx: any, userId: number): Promise<boolean> {
    const providerRole = await tx.role.findUnique({ where: { Name: 'Provider' } });
    if (!providerRole) {
      throw new Error('Provider role not found in database');
    }

    const existingRole = await tx.userRoleJunction.findFirst({
      where: {
        UserId: userId,
        RoleId: providerRole.Id
      }
    });

    if (!existingRole) {
      await tx.userRoleJunction.create({
        data: {
          UserId: userId,
          RoleId: providerRole.Id
        }
      });
      console.log(`[Provider Role] Granted Provider role to user ${userId}`);
      return true;
    } else {
      console.log(`[Provider Role] User ${userId} already has Provider role`);
      return false;
    }
  }

  /**
   * Process bulk action on all services in a request
   */
  static async processBulkAction(
    requestId: string,
    action: 'approve-all' | 'reject-all' | 'requires-changes-all',
    adminNotes?: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Get the request with all pending services
      const request = await tx.providerRegistrationRequest.findUnique({
        where: { Id: requestId },
        include: {
          PendingServices: {
            include: {
              Category: true,
              NannyServiceDetails: true,
              ElderCareServiceDetails: true,
              CleaningServiceDetails: true,
              TutoringServiceDetails: true,
              CookingServiceDetails: true,
            }
          }
        }
      });

      if (!request) {
        throw new Error('Provider registration request not found');
      }

      if (request.Status !== 'Pending') {
        throw new Error(`Request is already in '${request.Status}' status`);
      }

      const pendingServices = request.PendingServices.filter(s => s.Status === 'PendingReview');
      if (pendingServices.length === 0) {
        throw new Error('No pending services found for this request');
      }

      let newRequestStatus: 'Approved' | 'Rejected' | 'Pending';
      let serviceStatus: 'Approved' | 'Rejected' | 'RequiresChanges';
      let shouldCopyServices = false;

      switch (action) {
        case 'approve-all':
          serviceStatus = 'Approved';
          newRequestStatus = 'Approved';
          shouldCopyServices = true;
          break;
        case 'reject-all':
          serviceStatus = 'Rejected';
          newRequestStatus = 'Rejected';
          shouldCopyServices = true;
          break;
        case 'requires-changes-all':
          serviceStatus = 'RequiresChanges';
          newRequestStatus = 'Pending';
          shouldCopyServices = false;
          break;
      }

      // Update all pending services
      await tx.pendingService.updateMany({
        where: {
          RequestId: requestId,
          Status: 'PendingReview'
        },
        data: {
          Status: serviceStatus,
          AdminNotes: adminNotes || `Bulk ${action.replace('-', ' ')} by admin`,
          UpdatedAt: new Date()
        }
      });

      let copiedServicesCount = 0;
      let providerRoleGranted = false;

      // Copy services to live tables if needed
      if (shouldCopyServices) {
        for (const pendingService of pendingServices) {
          await this.copyPendingServiceToLive(
            tx,
            { ...pendingService, Request: request },
            serviceStatus as 'Approved' | 'Rejected'
          );
          copiedServicesCount++;
        }

        // Grant provider role if approving services
        if (action === 'approve-all') {
          providerRoleGranted = await this.grantProviderRole(tx, request.UserId);
        }
      }

      // Update request status
      await tx.providerRegistrationRequest.update({
        where: { Id: requestId },
        data: {
          Status: newRequestStatus,
          AdminNotes: adminNotes || `Bulk ${action.replace('-', ' ')} by admin`
        }
      });

      // Create notification
      const notificationMessage = this.getNotificationMessage(action, request.UserName);
      let notificationType: 'ProviderRequestApproved' | 'ProviderRequestRejected' | 'ServiceStatusChanged' = 'ServiceStatusChanged';

      if (action === 'approve-all') {
        notificationType = 'ProviderRequestApproved';
      } else if (action === 'reject-all') {
        notificationType = 'ProviderRequestRejected';
      }

      await tx.notification.create({
        data: {
          UserId: request.UserId,
          Message: notificationMessage,
          Link: action === 'approve-all' ? '/dashboard/provider' : '/register-provider',
          Type: notificationType,
        },
      });

      return {
        updatedServicesCount: pendingServices.length,
        copiedServicesCount,
        providerRoleGranted,
        newRequestStatus
      };
    });
  }

  /**
   * Get notification message based on action
   */
  private static getNotificationMessage(action: string, userName: string): string {
    switch (action) {
      case 'approve-all':
        return `Felicitări ${userName}! Cererea ta de a deveni prestator a fost aprobată.`;
      case 'reject-all':
        return `${userName}, cererea ta de prestator a fost respinsă. Te rugăm să verifici motivele și să reaplici.`;
      case 'requires-changes-all':
        return `${userName}, cererea ta de prestator necesită modificări. Te rugăm să revizuiești și să retrimitți.`;
      default:
        return `${userName}, statusul cererii tale de prestator a fost actualizat.`;
    }
  }

  /**
   * Process mixed actions on individual services in a request
   */
  static async processMixedAction(
    requestId: string,
    serviceActions: Array<{ serviceId: number; status: 'Approved' | 'Rejected' | 'RequiresChanges'; adminNotes?: string }>
  ) {
    return await prisma.$transaction(async (tx) => {
      // Get the request with all pending services
      const request = await tx.providerRegistrationRequest.findUnique({
        where: { Id: requestId },
        include: {
          PendingServices: {
            include: {
              Category: true,
              NannyServiceDetails: true,
              ElderCareServiceDetails: true,
              CleaningServiceDetails: true,
              TutoringServiceDetails: true,
              CookingServiceDetails: true,
            }
          }
        }
      });

      if (!request) {
        throw new Error('Provider registration request not found');
      }

      if (request.Status !== 'Pending') {
        throw new Error(`Request is already in '${request.Status}' status`);
      }

      let copiedServicesCount = 0;
      let providerRoleGranted = false;
      const updatedServices = [];

      // Process each service action
      for (const action of serviceActions) {
        const pendingService = request.PendingServices.find(s => s.Id === action.serviceId);
        if (!pendingService) {
          throw new Error(`Service with ID ${action.serviceId} not found in request`);
        }

        if (pendingService.Status !== 'PendingReview') {
          throw new Error(`Service ${action.serviceId} is not in PendingReview status`);
        }

        // Update the pending service status
        const updatedService = await tx.pendingService.update({
          where: { Id: action.serviceId },
          data: {
            Status: action.status,
            AdminNotes: action.adminNotes || `Individual ${action.status.toLowerCase()} by admin`,
            UpdatedAt: new Date()
          }
        });

        updatedServices.push(updatedService);

        // Copy to live table if approved or rejected (not for RequiresChanges)
        if (action.status === 'Approved' || action.status === 'Rejected') {
          await this.copyPendingServiceToLive(
            tx,
            { ...pendingService, Request: request },
            action.status
          );
          copiedServicesCount++;
        }
      }

      // Get all services after updates to calculate request status
      const allServices = await tx.pendingService.findMany({
        where: { RequestId: requestId }
      });

      // Calculate new request status
      const newRequestStatus = this.calculateRequestStatus(allServices);

      // Grant provider role if at least one service is approved
      if (this.shouldGrantProviderRole(allServices)) {
        providerRoleGranted = await this.grantProviderRole(tx, request.UserId);
      }

      // Update request status
      await tx.providerRegistrationRequest.update({
        where: { Id: requestId },
        data: {
          Status: newRequestStatus,
          AdminNotes: `Individual service actions processed by admin`
        }
      });

      // Create notification based on overall status
      let notificationMessage = '';
      let notificationType: 'ProviderRequestApproved' | 'ProviderRequestRejected' | 'ServiceStatusChanged' = 'ServiceStatusChanged';
      let notificationLink = '';

      if (newRequestStatus === 'Approved') {
        notificationMessage = `Felicitări ${request.UserName}! Cererea ta de a deveni prestator a fost aprobată.`;
        notificationType = 'ProviderRequestApproved';
        notificationLink = '/dashboard/provider';
      } else if (newRequestStatus === 'Rejected') {
        notificationMessage = `${request.UserName}, cererea ta de prestator a fost respinsă.`;
        notificationType = 'ProviderRequestRejected';
        notificationLink = '/register-provider';
      } else {
        notificationMessage = `${request.UserName}, statusul serviciilor tale a fost actualizat.`;
        notificationType = 'ServiceStatusChanged';
        notificationLink = '/register-provider';
      }

      await tx.notification.create({
        data: {
          UserId: request.UserId,
          Message: notificationMessage,
          Link: notificationLink,
          Type: notificationType,
        },
      });

      return {
        updatedServicesCount: serviceActions.length,
        copiedServicesCount,
        providerRoleGranted,
        newRequestStatus,
        updatedServices
      };
    });
  }

  /**
   * Update service status (for admin actions)
   */
  static async updateServiceStatus(
    serviceId: number,
    status: PendingServiceStatus,
    adminNotes?: string
  ) {
    return await prisma.pendingService.update({
      where: { Id: serviceId },
      data: {
        Status: status,
        AdminNotes: adminNotes,
        UpdatedAt: new Date(),
      }
    });
  }
}
