
"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner, ErrorState, EmptyState } from "@/components/ui/loading-error-states";
import { Loader2, PlusCircle, Edit, Trash2, MapPin, AlertTriangle, RefreshCw } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { AddressService, LocationService, type LocationEntry } from '@repo/services';
import type { Address, AddressPayload } from '@repo/types';
import { LocationCombobox } from '@/components/ui/location-combobox';

const addressPageTranslations = {
  pageTitle: { ro: "Adresele Mele", ru: "Мои адреса", en: "My Addresses" },
  pageDescription: { ro: "Gestionează adresele tale de livrare sau de prestare a serviciilor.", ru: "Управляйте своими адресами доставки или оказания услуг.", en: "Manage your delivery or service addresses." },
  addNewAddress: { ro: "Adaugă Adresă Nouă", ru: "Добавить новый адрес", en: "Add New Address" },
  noAddresses: { ro: "Nu ai adăugat nicio adresă încă.", ru: "Вы еще не добавили ни одного адреса.", en: "You haven't added any addresses yet." },
  editAddressTitle: { ro: "Editează Adresa", ru: "Редактировать адрес", en: "Edit Address" },
  addAddressTitle: { ro: "Adaugă o Adresă Nouă", ru: "Добавить новый адрес", en: "Add a New Address" },
  labelLabel: { ro: "Etichetă (ex: Acasă, Birou)", ru: "Метка (напр. Дом, Офис)", en: "Label (e.g. Home, Office)" },
  streetLabel: { ro: "Stradă și număr", ru: "Улица и номер", en: "Street and number" },
  cityLabel: { ro: "Oraș/Localitate", ru: "Город/Населенный пункт", en: "City/Town" },
  regionLabel: { ro: "Raion/Regiune (opțional)", ru: "Район/Регион (необязательно)", en: "Region (optional)" },
  postalCodeLabel: { ro: "Cod Poștal (opțional)", ru: "Почтовый индекс (необязательно)", en: "Postal Code (optional)" },
  saveButton: { ro: "Salvează", ru: "Сохранить", en: "Save" },
  savingButton: { ro: "Se salvează...", ru: "Сохранение...", en: "Saving..." },
  deleteConfirmTitle: { ro: "Ești sigur că vrei să ștergi această adresă?", ru: "Вы уверены, что хотите удалить этот адрес?", en: "Are you sure you want to delete this address?" },
  deleteConfirmDescription: { ro: "Această acțiune este ireversibilă.", ru: "Это действие необратимо.", en: "This action is irreversible." },
};

export default function AddressSettingsPage() {
  const { translate, currentLanguage } = useLanguage();
  const { toast } = useToast();

  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<AddressPayload | null>(null);
  const [editingAddressId, setEditingAddressId] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Location state
  const [locations, setLocations] = useState<LocationEntry[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);

  const fetchAddresses = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await AddressService.getAddresses();
      setAddresses(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "A apărut o eroare la preluarea adreselor.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAddresses();
  }, [fetchAddresses]);

  // Load locations
  useEffect(() => {
    const loadLocations = async () => {
      setLocationsLoading(true);
      try {
        const locationData = await LocationService.getLocationsForForms(currentLanguage.code);
        setLocations(locationData);
      } catch (error) {
        console.error('Error loading locations:', error);
      } finally {
        setLocationsLoading(false);
      }
    };

    loadLocations();
  }, [currentLanguage.code]);

  const openFormForNew = () => {
    setCurrentAddress({
      label: '',
      street: '',
      city: '',
      region: '',
      postalCode: '',
      isDefault: false,
      countryId: null,
      regionId: null,
      cityId: null,
      sectorId: null
    });
    setEditingAddressId(null);
    setIsFormOpen(true);
  };

  const openFormForEdit = (address: Address) => {
    setCurrentAddress({
      label: address.label,
      street: address.street,
      city: address.city,
      region: address.region,
      postalCode: address.postalCode,
      isDefault: address.isDefault,
    });
    setEditingAddressId(address.id);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async () => {
    if (!currentAddress) return;
    setIsLoading(true);
    try {
      if (editingAddressId) {
        await AddressService.updateAddress(editingAddressId, currentAddress);
        toast({ title: "Succes", description: "Adresa a fost actualizată." });
      } else {
        await AddressService.createAddress(currentAddress);
        toast({ title: "Succes", description: "Adresa a fost adăugată." });
      }
      setIsFormOpen(false);
      fetchAddresses();
    } catch (err) {
      toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAddress = async (id: number) => {
    try {
      await AddressService.deleteAddress(id);
      toast({ title: "Succes", description: "Adresa a fost ștearsă." });
      fetchAddresses();
    } catch (err) {
      toast({ variant: "destructive", title: "Eroare", description: err instanceof Error ? err.message : "A apărut o eroare." });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentAddress(prev => prev ? { ...prev, [name]: value } : null);
  };

  return (
    <div className="container mx-auto max-w-4xl space-y-6">
      <Card className="shadow-sm">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0 pb-6">
          <div className="space-y-1">
            <CardTitle className="text-2xl font-headline flex items-center">
              <MapPin className="w-6 h-6 mr-3 text-primary" />
              {translate(addressPageTranslations, 'pageTitle')}
            </CardTitle>
            <CardDescription className="text-base">
              {translate(addressPageTranslations, 'pageDescription')}
            </CardDescription>
          </div>
          <Button onClick={openFormForNew} className="shrink-0">
            <PlusCircle className="w-4 h-4 mr-2" />
            {translate(addressPageTranslations, 'addNewAddress')}
          </Button>
        </CardHeader>
        <CardContent className="pt-0">
          {isLoading ? (
            <LoadingSpinner message="Se încarcă adresele..." size="lg" />
          ) : error ? (
            <ErrorState
              title="A apărut o eroare"
              message={error}
              onRetry={fetchAddresses}
              retryLabel="Încearcă din nou"
              type="generic"
            />
          ) : addresses.length === 0 ? (
            <EmptyState
              title="Nicio adresă salvată"
              message={translate(addressPageTranslations, 'noAddresses')}
              icon={<MapPin className="w-8 h-8 text-muted-foreground" />}
              action={{
                label: "Adaugă prima adresă",
                onClick: openFormForNew
              }}
            />
          ) : (
            <div className="grid gap-4">
              {addresses.map(address => (
                <Card key={address.id} className="p-6 bg-gradient-to-r from-background to-muted/20 border-l-4 border-l-primary/20 hover:border-l-primary/40 transition-colors">
                  <div className="flex flex-col sm:flex-row sm:items-start justify-between space-y-4 sm:space-y-0">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg">{address.label}</h3>
                        {address.isDefault && (
                          <Badge variant="secondary" className="text-xs">
                            Implicit
                          </Badge>
                        )}
                      </div>
                      <div className="space-y-1 text-muted-foreground">
                        <p className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          {address.street}
                        </p>
                        <p className="text-sm ml-6">
                          {address.city}{address.region && `, ${address.region}`}
                          {address.postalCode && ` ${address.postalCode}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2 shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openFormForEdit(address)}
                        className="flex items-center gap-2"
                      >
                        <Edit className="w-4 h-4" />
                        Editează
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>{translate(addressPageTranslations, 'deleteConfirmTitle')}</AlertDialogTitle>
                            <AlertDialogDescription>{translate(addressPageTranslations, 'deleteConfirmDescription')}</AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>{translate(commonTranslations, 'cancelButton')}</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteAddress(address.id)}>Șterge</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="space-y-3">
            <DialogTitle className="text-xl">
              {editingAddressId ? translate(addressPageTranslations, 'editAddressTitle') : translate(addressPageTranslations, 'addAddressTitle')}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="label" className="text-sm font-medium">
                {translate(addressPageTranslations, 'labelLabel')}
              </Label>
              <Input
                id="label"
                name="label"
                value={currentAddress?.label || ''}
                onChange={handleInputChange}
                placeholder="ex: Acasă, Birou, etc."
                className="h-11"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="street" className="text-sm font-medium">
                {translate(addressPageTranslations, 'streetLabel')}
              </Label>
              <Input
                id="street"
                name="street"
                value={currentAddress?.street || ''}
                onChange={handleInputChange}
                placeholder="ex: Strada Ștefan cel Mare 123"
                className="h-11"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location" className="text-sm font-medium">
                {translate(commonTranslations, 'locationLabel')}
              </Label>
              <LocationCombobox
                locations={locations}
                value={currentAddress?.sectorId ? (locations.find(loc => loc.id === currentAddress.sectorId)?.slug || "") : ""}
                onValueChange={(val) => {
                  // Convert slug to location hierarchy and update all location fields
                  const location = locations.find(loc => loc.slug === val);
                  if (location) {
                    setCurrentAddress(prev => ({
                      ...prev,
                      sectorId: location.id,
                      // Also update backward compatibility fields
                      city: location.name,
                      region: location.type === 'Municipality' ? location.name : prev?.region
                    }));
                  } else {
                    setCurrentAddress(prev => ({
                      ...prev,
                      sectorId: null,
                      city: '',
                      region: ''
                    }));
                  }
                }}
                placeholder={locationsLoading ? "Se încarcă locațiile..." : translate(commonTranslations, 'locationPlaceholder')}
                disabled={locationsLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode" className="text-sm font-medium">
                {translate(addressPageTranslations, 'postalCodeLabel')}
              </Label>
              <Input
                id="postalCode"
                name="postalCode"
                value={currentAddress?.postalCode || ''}
                onChange={handleInputChange}
                placeholder="ex: MD-2001"
                className="h-11"
              />
            </div>
          </div>
          <DialogFooter className="gap-2 sm:gap-0">
            <DialogClose asChild>
              <Button variant="outline" className="flex-1 sm:flex-none">
                {translate(commonTranslations, 'cancelButton')}
              </Button>
            </DialogClose>
            <Button
              onClick={handleFormSubmit}
              disabled={isSaving || !currentAddress?.label || !currentAddress?.street}
              className="flex-1 sm:flex-none"
            >
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {translate(addressPageTranslations, 'saveButton')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
