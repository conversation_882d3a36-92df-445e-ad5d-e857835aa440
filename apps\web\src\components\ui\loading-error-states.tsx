"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Loader2, 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  Server, 
  Database,
  FileX,
  Search,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Loading States
interface LoadingStateProps {
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function LoadingSpinner({ message = "Se încarcă...", className, size = 'md' }: LoadingStateProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={cn("flex flex-col items-center justify-center py-8 space-y-3", className)}>
      <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
      {message && (
        <p className="text-sm text-muted-foreground text-center max-w-sm">{message}</p>
      )}
    </div>
  );
}

export function LoadingCard({ title, className }: { title?: string; className?: string }) {
  return (
    <Card className={cn("", className)}>
      {title && (
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-16" />
        </div>
      </CardContent>
    </Card>
  );
}

export function LoadingTable({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </div>
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-8 w-full" />
          ))}
        </div>
      ))}
    </div>
  );
}

// Error States
interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryLabel?: string;
  className?: string;
  type?: 'generic' | 'network' | 'server' | 'notFound' | 'timeout';
}

export function ErrorState({ 
  title, 
  message, 
  onRetry, 
  retryLabel = "Încearcă din nou",
  className,
  type = 'generic'
}: ErrorStateProps) {
  const getIcon = () => {
    switch (type) {
      case 'network':
        return <Wifi className="w-8 h-8 text-destructive" />;
      case 'server':
        return <Server className="w-8 h-8 text-destructive" />;
      case 'notFound':
        return <FileX className="w-8 h-8 text-destructive" />;
      case 'timeout':
        return <Clock className="w-8 h-8 text-destructive" />;
      default:
        return <AlertTriangle className="w-8 h-8 text-destructive" />;
    }
  };

  const getDefaultTitle = () => {
    switch (type) {
      case 'network':
        return 'Probleme de conexiune';
      case 'server':
        return 'Eroare de server';
      case 'notFound':
        return 'Nu s-a găsit';
      case 'timeout':
        return 'Timpul a expirat';
      default:
        return 'A apărut o eroare';
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'network':
        return 'Verifică conexiunea la internet și încearcă din nou.';
      case 'server':
        return 'Serverul nu răspunde momentan. Te rugăm să încerci mai târziu.';
      case 'notFound':
        return 'Informația căutată nu a fost găsită.';
      case 'timeout':
        return 'Cererea a durat prea mult. Te rugăm să încerci din nou.';
      default:
        return 'Ceva nu a mers bine. Te rugăm să încerci din nou.';
    }
  };

  return (
    <div className={cn("flex flex-col items-center justify-center py-16 space-y-4", className)}>
      <div className="p-3 bg-destructive/10 rounded-full">
        {getIcon()}
      </div>
      <div className="text-center space-y-2 max-w-md">
        <h3 className="font-medium text-destructive">
          {title || getDefaultTitle()}
        </h3>
        <p className="text-sm text-muted-foreground">
          {message || getDefaultMessage()}
        </p>
        {onRetry && (
          <Button variant="outline" onClick={onRetry} className="mt-4">
            <RefreshCw className="w-4 h-4 mr-2" />
            {retryLabel}
          </Button>
        )}
      </div>
    </div>
  );
}

// Empty States
interface EmptyStateProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}

export function EmptyState({ 
  title = "Niciun rezultat", 
  message = "Nu s-au găsit date pentru afișare.",
  action,
  icon,
  className 
}: EmptyStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center py-16 space-y-4", className)}>
      <div className="p-4 bg-muted/50 rounded-full">
        {icon || <Search className="w-8 h-8 text-muted-foreground" />}
      </div>
      <div className="text-center space-y-2 max-w-md">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">{message}</p>
        {action && (
          <Button onClick={action.onClick} className="mt-4">
            {action.label}
          </Button>
        )}
      </div>
    </div>
  );
}

// Composite Loading States
export function PageLoading({ title }: { title?: string }) {
  return (
    <div className="space-y-6">
      {title && <Skeleton className="h-8 w-64" />}
      <div className="grid gap-6">
        <LoadingCard />
        <LoadingCard />
        <LoadingCard />
      </div>
    </div>
  );
}

export function ListLoading({ itemCount = 5 }: { itemCount?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: itemCount }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      ))}
    </div>
  );
}
