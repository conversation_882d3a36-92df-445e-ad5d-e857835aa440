import { type NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import jwt from 'jsonwebtoken';
import apiFetch from '@/lib/api-client';

async function handler(req: NextRequest) {
  const path = req.nextUrl.pathname.replace('/api/proxy', '');
  const search = req.nextUrl.search; // Get the query string, e.g., "?clientId=2"
  const endpointWithQuery = `${path}${search}`; // Combine them

  // 1. Decodează token-ul de sesiune NextAuth (JWE) pentru a obține datele utilizatorului
  const sessionToken = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  
  let internalApiJwt: string | null = null;
  
  // 2. Dacă utilizatorul este logat, creează un NOU token, simplu (JWT), pentru API-ul intern
  if (sessionToken) {
    const payload = {
      id: sessionToken.id,
      roles: sessionToken.roles,
      isAdmin: sessionToken.isAdmin,
      // Poți adăuga aici și alte date din sesiune de care API-ul ar putea avea nevoie
    };

    // Semnează noul token cu același secret
    internalApiJwt = jwt.sign(
      payload,
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '5m' } // Un token cu viață scurtă, suficient pentru o călătorie a cererii
    );
  }

  try {
    // 3. Pasează noul JWT (sau null dacă utilizatorul nu e logat) către helper-ul apiFetch
    const apiResponse = await apiFetch(endpointWithQuery, { // Use the full path with query
      method: req.method,
      body: req.method !== 'GET' ? await req.text() : undefined,
      jwt: internalApiJwt,
    });

    // Check if the response is JSON
    const contentType = apiResponse.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await apiResponse.text();
      console.error(`[API Proxy] Non-JSON response for path ${endpointWithQuery}:`, {
        status: apiResponse.status,
        contentType,
        responseText: textResponse.substring(0, 500) // Log first 500 chars
      });

      return NextResponse.json(
        {
          message: `API returned non-JSON response (${apiResponse.status})`,
          details: apiResponse.status >= 500 ? 'Internal server error' : 'Invalid response format'
        },
        { status: apiResponse.status >= 400 ? apiResponse.status : 502 }
      );
    }

    const data = await apiResponse.json();
    return NextResponse.json(data, { status: apiResponse.status });

  } catch (error) {
    console.error(`[API Proxy] Error for path ${endpointWithQuery}:`, error);

    // Provide more specific error messages
    let errorMessage = 'API Proxy Error';
    let statusCode = 500;

    if (error instanceof SyntaxError && error.message.includes('JSON')) {
      errorMessage = 'Invalid JSON response from API';
      statusCode = 502;
    } else if (error instanceof TypeError && error.message.includes('fetch')) {
      errorMessage = 'Failed to connect to API server';
      statusCode = 503;
    }

    return NextResponse.json({ message: errorMessage }, { status: statusCode });
  }
}

export { handler as GET, handler as POST, handler as PUT, handler as DELETE, handler as PATCH };
