"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { LoadingSpinner, EmptyState } from "@/components/ui/loading-error-states";
import {
  CalendarIcon,
  Clock,
  MapPin,
  User,
  Star,
  CheckCircle,
  XCircle,
  Loader2,
  <PERSON><PERSON><PERSON>riangle,
  ExternalLink
} from "lucide-react";
import { format } from "date-fns";
import { ro } from "date-fns/locale";
import { useLanguage } from '@/contexts/language-context';
import { cn } from "@/lib/utils";
import Link from "next/link";

interface BookingFlowProps {
  serviceId: number;
  providerId: number;
  serviceName: string;
  providerName: string;
  providerRating?: number;
  servicePrice?: number;
  serviceDuration?: number;
  serviceIdForLink?: number; // Added for provider profile link
  onBookingComplete?: (bookingId: number) => void;
  onCancel?: () => void;
}

interface Address {
  id: number;
  street: string;
  city: string;
  region: string;
  country: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
}

const bookingTranslations = {
  bookService: { ro: "Rezervă Serviciul", ru: "Забронировать услугу", en: "Book Service" },
  selectDateTime: { ro: "Selectează Data și Ora", ru: "Выберите дату и время", en: "Select Date & Time" },
  selectDate: { ro: "Selectează Data", ru: "Выберите дату", en: "Select Date" },
  selectTime: { ro: "Selectează Ora", ru: "Выберите время", en: "Select Time" },
  selectAddress: { ro: "Selectează Adresa", ru: "Выберите адрес", en: "Select Address" },
  addNotes: { ro: "Adaugă Notițe", ru: "Добавить заметки", en: "Add Notes" },
  notesPlaceholder: { ro: "Detalii suplimentare sau cerințe speciale...", ru: "Дополнительные детали или особые требования...", en: "Additional details or special requirements..." },
  confirmBooking: { ro: "Confirmă Rezervarea", ru: "Подтвердить бронирование", en: "Confirm Booking" },
  cancel: { ro: "Anulează", ru: "Отмена", en: "Cancel" },
  bookingSuccess: { ro: "Rezervare Trimisă!", ru: "Бронирование отправлено!", en: "Booking Sent!" },
  bookingSuccessDesc: { ro: "Cererea ta de rezervare a fost trimisă furnizorului. Vei primi o notificare când va fi confirmată.", ru: "Ваш запрос на бронирование был отправлен поставщику. Вы получите уведомление, когда он будет подтвержден.", en: "Your booking request has been sent to the provider. You'll receive a notification when it's confirmed." },
  bookingError: { ro: "Eroare la Rezervare", ru: "Ошибка бронирования", en: "Booking Error" },
  loadingAvailability: { ro: "Se încarcă disponibilitatea...", ru: "Загрузка доступности...", en: "Loading availability..." },
  noAvailableSlots: { ro: "Nu sunt sloturi disponibile pentru această dată", ru: "Нет доступных слотов на эту дату", en: "No available slots for this date" },
  selectAddressFirst: { ro: "Selectează o adresă", ru: "Выберите адрес", en: "Select an address" },
  noAddressesSaved: { ro: "Nu ai adrese salvate", ru: "У вас нет сохраненных адресов", en: "No saved addresses" },
  manageAddresses: { ro: "Gestionează Adresele", ru: "Управление адресами", en: "Manage Addresses" },
  serviceDetails: { ro: "Detalii Serviciu", ru: "Детали услуги", en: "Service Details" },
  provider: { ro: "Furnizor", ru: "Поставщик", en: "Provider" },
  duration: { ro: "Durată", ru: "Продолжительность", en: "Duration" },
  price: { ro: "Preț", ru: "Цена", en: "Price" },
  minutes: { ro: "minute", ru: "минут", en: "minutes" },
  lei: { ro: "lei", ru: "лей", en: "lei" },
};

export function BookingFlow({
  serviceId,
  providerId,
  serviceName,
  providerName,
  providerRating = 0,
  servicePrice,
  serviceDuration,
  serviceIdForLink,
  onBookingComplete,
  onCancel
}: BookingFlowProps) {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [selectedAddressId, setSelectedAddressId] = useState<string>("");
  const [clientNotes, setClientNotes] = useState("");
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingSuccess, setBookingSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user addresses
  useEffect(() => {
    const loadAddresses = async () => {
      if (!session?.user) return;

      try {
        const response = await fetch(`/api/proxy/address`);
        if (response.ok) {
          const data = await response.json();
          // Ensure we have valid address data with proper structure
          const validAddresses = (data.addresses || []).filter((addr: any) =>
            addr &&
            typeof addr === 'object' &&
            (addr.id || addr.Id) &&
            (addr.street || addr.Street)
          ).map((addr: any) => ({
            // Normalize the address structure to handle both camelCase and PascalCase
            id: addr.id || addr.Id,
            label: addr.label || addr.Label || 'Adresă',
            street: addr.street || addr.Street || '',
            city: addr.city || addr.City || '',
            region: addr.region || addr.Region || '',
            postalCode: addr.postalCode || addr.PostalCode || '',
            isDefault: addr.isDefault || addr.IsDefault || false
          }));

          setAddresses(validAddresses);
        } else {
          console.error('Failed to load addresses:', response.status, response.statusText);
          setAddresses([]);
        }
      } catch (error) {
        console.error('Error loading addresses:', error);
        setAddresses([]);
      } finally {
        setIsLoadingAddresses(false);
      }
    };

    loadAddresses();
  }, [session]);

  // Load available time slots when date changes
  useEffect(() => {
    const loadAvailableSlots = async () => {
      if (!selectedDate) {
        setAvailableSlots([]);
        return;
      }

      setIsLoadingSlots(true);
      try {
        const dateStr = format(selectedDate, 'yyyy-MM-dd');
        const response = await fetch(`/api/proxy/providers/${providerId}/availability?date=${dateStr}`);
        
        if (response.ok) {
          const data = await response.json();
          setAvailableSlots(data.slots || []);
        } else {
          // Generate default time slots if API not available
          const defaultSlots: TimeSlot[] = [];
          for (let hour = 9; hour <= 17; hour++) {
            defaultSlots.push({
              time: `${hour.toString().padStart(2, '0')}:00`,
              available: Math.random() > 0.3 // 70% availability simulation
            });
            if (hour < 17) {
              defaultSlots.push({
                time: `${hour.toString().padStart(2, '0')}:30`,
                available: Math.random() > 0.3
              });
            }
          }
          setAvailableSlots(defaultSlots);
        }
      } catch (error) {
        console.error('Error loading availability:', error);
        setAvailableSlots([]);
      } finally {
        setIsLoadingSlots(false);
      }
    };

    loadAvailableSlots();
  }, [selectedDate, providerId]);

  const handleSubmitBooking = async () => {
    if (!selectedDate || !selectedTime || !selectedAddressId) {
      setError("Vă rugăm să completați toate câmpurile obligatorii.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const eventStartDateTime = new Date(selectedDate);
      const [hours, minutes] = selectedTime.split(':').map(Number);
      eventStartDateTime.setHours(hours, minutes, 0, 0);

      const eventEndDateTime = new Date(eventStartDateTime);
      if (serviceDuration) {
        eventEndDateTime.setMinutes(eventEndDateTime.getMinutes() + serviceDuration);
      } else {
        eventEndDateTime.setHours(eventEndDateTime.getHours() + 1); // Default 1 hour
      }

      const response = await fetch('/api/proxy/bookings/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerId,
          serviceId,
          eventStartDateTime: eventStartDateTime.toISOString(),
          eventEndDateTime: eventEndDateTime.toISOString(),
          clientNotes,
          selectedAddressId: parseInt(selectedAddressId),
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setBookingSuccess(true);
        if (onBookingComplete) {
          onBookingComplete(data.booking.id);
        }
      } else {
        setError(data.message || 'A apărut o eroare la crearea rezervării.');
      }
    } catch (error) {
      console.error('Booking error:', error);
      setError('A apărut o eroare la crearea rezervării.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (bookingSuccess) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold text-green-700">
                {translate(bookingTranslations, 'bookingSuccess')}
              </h3>
              <p className="text-muted-foreground mt-2">
                {translate(bookingTranslations, 'bookingSuccessDesc')}
              </p>
            </div>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => window.location.href = '/dashboard/client/bookings'}>
                Vezi Rezervările Mele
              </Button>
              <Button variant="outline" onClick={onCancel}>
                Închide
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{translate(bookingTranslations, 'bookService')}</CardTitle>
        <CardDescription>
          Completează detaliile pentru a rezerva acest serviciu
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Service Details */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-6 rounded-xl">
          <h4 className="font-semibold text-lg mb-4 text-blue-900 flex items-center gap-2">
            <User className="w-5 h-5" />
            {translate(bookingTranslations, 'serviceDetails')}
          </h4>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-blue-700 font-medium">Serviciu:</span>
              <span className="font-semibold text-blue-900 bg-white px-3 py-1 rounded-full text-sm">{serviceName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-700 font-medium">{translate(bookingTranslations, 'provider')}:</span>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <span className="font-semibold text-blue-900">{providerName}</span>
                  {providerRating > 0 && (
                    <div className="flex items-center gap-1 bg-yellow-100 px-2 py-1 rounded-full">
                      <Star className="w-3 h-3 fill-yellow-500 text-yellow-500" />
                      <span className="text-xs font-medium text-yellow-700">{providerRating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
                {serviceIdForLink && serviceIdForLink !== -1 && (
                  <Button asChild variant="ghost" size="sm" className="h-8 px-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100">
                    <Link href={`/profile/${providerId}/${serviceIdForLink}`} className="flex items-center gap-1">
                      <ExternalLink className="w-3 h-3" />
                      <span className="text-xs">Vezi Profil</span>
                    </Link>
                  </Button>
                )}
              </div>
            </div>
            {serviceDuration && (
              <div className="flex justify-between items-center">
                <span className="text-blue-700 font-medium flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {translate(bookingTranslations, 'duration')}:
                </span>
                <span className="font-semibold text-blue-900 bg-white px-3 py-1 rounded-full text-sm">
                  {Math.floor(serviceDuration / 60) > 0 && `${Math.floor(serviceDuration / 60)}h `}
                  {serviceDuration % 60 > 0 && `${serviceDuration % 60}min`}
                </span>
              </div>
            )}
            {servicePrice && (
              <div className="flex justify-between items-center">
                <span className="text-blue-700 font-medium">{translate(bookingTranslations, 'price')}:</span>
                <span className="font-bold text-lg text-green-700 bg-green-100 px-3 py-1 rounded-full">
                  {servicePrice} {translate(bookingTranslations, 'lei')}/oră
                </span>
              </div>
            )}
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{translate(bookingTranslations, 'bookingError')}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Date Selection */}
        <div className="space-y-4">
          <Label className="text-base font-semibold flex items-center gap-2 text-gray-800">
            <CalendarIcon className="w-5 h-5 text-blue-600" />
            {translate(bookingTranslations, 'selectDate')}
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-medium h-12 border-2 hover:border-blue-300 transition-colors",
                  !selectedDate && "text-muted-foreground",
                  selectedDate && "border-blue-200 bg-blue-50"
                )}
              >
                <CalendarIcon className="mr-3 h-5 w-5 text-blue-600" />
                {selectedDate ? format(selectedDate, "PPP", { locale: ro }) : "Selectează data"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                disabled={(date) => date < new Date() || date < new Date("1900-01-01")}
                initialFocus
                className="rounded-md border"
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Time Selection */}
        {selectedDate && (
          <div className="space-y-4">
            <Label className="text-base font-semibold flex items-center gap-2 text-gray-800">
              <Clock className="w-5 h-5 text-blue-600" />
              {translate(bookingTranslations, 'selectTime')}
            </Label>
            {isLoadingSlots ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <LoadingSpinner
                  message={translate(bookingTranslations, 'loadingAvailability')}
                  size="md"
                />
              </div>
            ) : availableSlots.length > 0 ? (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-3">
                  {availableSlots.map((slot) => (
                    <Button
                      key={slot.time}
                      variant={selectedTime === slot.time ? "default" : "outline"}
                      size="sm"
                      disabled={!slot.available}
                      onClick={() => setSelectedTime(slot.time)}
                      className={cn(
                        "text-sm font-medium transition-all h-10",
                        selectedTime === slot.time && "ring-2 ring-blue-300 bg-blue-600 hover:bg-blue-700",
                        !slot.available && "opacity-50 cursor-not-allowed",
                        slot.available && selectedTime !== slot.time && "hover:bg-blue-50 hover:border-blue-300"
                      )}
                    >
                      {slot.time}
                    </Button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <EmptyState
                  title="Nu sunt ore disponibile"
                  message={translate(bookingTranslations, 'noAvailableSlots')}
                  icon={<Clock className="w-6 h-6 text-yellow-600" />}
                />
              </div>
            )}
          </div>
        )}

        {/* Address Selection */}
        <div className="space-y-4">
          <Label className="text-base font-semibold flex items-center gap-2 text-gray-800">
            <MapPin className="w-5 h-5 text-blue-600" />
            {translate(bookingTranslations, 'selectAddress')}
          </Label>
          {isLoadingAddresses ? (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <LoadingSpinner message="Se încarcă adresele..." size="sm" />
            </div>
          ) : addresses.length > 0 ? (
            <Select value={selectedAddressId} onValueChange={setSelectedAddressId}>
              <SelectTrigger className={cn(
                "h-12 border-2 hover:border-blue-300 transition-colors",
                selectedAddressId && "border-blue-200 bg-blue-50"
              )}>
                <SelectValue placeholder={translate(bookingTranslations, 'selectAddressFirst')} />
              </SelectTrigger>
              <SelectContent>
                {addresses.filter(address => address && address.id).map((address) => (
                  <SelectItem key={address.id} value={address.id.toString()}>
                    <div className="flex items-start gap-3 py-2">
                      <MapPin className="w-4 h-4 mt-0.5 text-blue-600" />
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{address.label || 'Adresă fără nume'}</p>
                        <p className="text-sm text-gray-600">
                          {address.street || 'Strada necunoscută'}{address.city ? `, ${address.city}` : ''}
                        </p>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="border-2 border-dashed border-orange-200 bg-orange-50 rounded-lg">
              <EmptyState
                title="Nicio adresă salvată"
                message={translate(bookingTranslations, 'noAddressesSaved')}
                icon={<MapPin className="w-6 h-6 text-orange-600" />}
                action={{
                  label: translate(bookingTranslations, 'manageAddresses'),
                  onClick: () => window.location.href = '/dashboard/settings/address'
                }}
              />
            </div>
          )}
        </div>

        {/* Notes */}
        <div className="space-y-4">
          <Label className="text-base font-semibold text-gray-800">
            {translate(bookingTranslations, 'addNotes')}
            <span className="text-sm text-gray-500 font-normal ml-2">(opțional)</span>
          </Label>
          <Textarea
            placeholder={translate(bookingTranslations, 'notesPlaceholder')}
            value={clientNotes}
            onChange={(e) => setClientNotes(e.target.value)}
            rows={4}
            className="resize-none border-2 hover:border-blue-300 focus:border-blue-500 transition-colors"
          />
        </div>

        {/* Booking Summary */}
        {selectedDate && selectedTime && selectedAddressId && (
          <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
            <h4 className="font-semibold text-lg mb-4 text-green-900 flex items-center gap-2">
              <CheckCircle className="w-5 h-5" />
              Rezumatul rezervării
            </h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-green-800">
                <CalendarIcon className="w-4 h-4" />
                <span className="font-medium">{format(selectedDate, "PPP", { locale: ro })}</span>
              </div>
              <div className="flex items-center gap-3 text-green-800">
                <Clock className="w-4 h-4" />
                <span className="font-medium">{selectedTime}</span>
                {serviceDuration && (
                  <span className="text-sm text-green-600">
                    ({Math.floor(serviceDuration / 60) > 0 && `${Math.floor(serviceDuration / 60)}h `}
                    {serviceDuration % 60 > 0 && `${serviceDuration % 60}min`})
                  </span>
                )}
              </div>
              <div className="flex items-center gap-3 text-green-800">
                <MapPin className="w-4 h-4" />
                <span className="font-medium">{addresses.find(a => a && a.id && a.id.toString() === selectedAddressId)?.street || 'Adresă selectată'}</span>
              </div>
              {servicePrice && (
                <div className="flex items-center gap-3 text-green-800 pt-2 border-t border-green-200">
                  <span className="font-semibold">Preț estimat: {servicePrice} lei/oră</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-8">
          <Button
            onClick={handleSubmitBooking}
            disabled={!selectedDate || !selectedTime || !selectedAddressId || isSubmitting}
            className="flex-1 h-12 text-base font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all"
            size="lg"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-3" />
                Se trimite rezervarea...
              </>
            ) : (
              <>
                <CheckCircle className="w-5 h-5 mr-3" />
                {translate(bookingTranslations, 'confirmBooking')}
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            className="sm:w-auto h-12 text-base font-medium border-2 hover:bg-gray-50 transition-colors"
            size="lg"
          >
            <XCircle className="w-5 h-5 mr-3" />
            {translate(bookingTranslations, 'cancel')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
