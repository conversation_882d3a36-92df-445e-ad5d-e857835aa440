"use client";

import Link from 'next/link';
import { Navbar } from '@/components/layout/navbar';
import { Separator } from '@/components/ui/separator';
import { User, Settings, LayoutDashboard, ListChecks, CalendarCheck, CalendarClock, Briefcase, MessageSquare, Loader2, MapPin, ShieldAlert } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import type { ExtendedSession } from '@repo/auth';
import { RoleIndicator } from '@/components/dashboard/role-indicator';
import { useNavigationConfig, getRoleFromPath, isNavigationItemActive, trackNavigationClick, type NavigationItem } from '@/components/dashboard/navigation-config';
import { OnboardingTooltips, NewProviderWelcomeBanner, useOnboarding } from '@/components/dashboard/onboarding-tooltips';

const dashboardLayoutTranslations = {
  userPanelTitle: { ro: "Panou Utilizator", ru: "Панель пользователя", en: "User Panel" },
  loadingUserData: { ro: "Se încarcă datele utilizatorului...", ru: "Загрузка данных пользователя...", en: "Loading user data..." },
  clientSectionTitle: { ro: "Client", ru: "Клиент", en: "Client" },
  providerSectionTitle: { ro: "Prestator", ru: "Поставщик", en: "Provider" },
  adminAccessRestrictedTitle: { ro: "Acces Restricționat", ru: "Доступ ограничен", en: "Access Restricted" },
  adminAccessRestrictedDesc: { ro: "Administratorii nu au acces la panoul de client. Te rugăm să folosești panoul de administrare.", ru: "Администраторы не имеют доступа к панели клиента. Пожалуйста, используйте панель администратора.", en: "Administrators do not have access to the client panel. Please use the admin panel." },
  goToAdminPanel: { ro: "Mergi la Panoul de Admin", ru: "Перейти в панель администратора", en: "Go to Admin Panel" },
};

interface DashboardLayoutClientProps {
  children: React.ReactNode;
  session: ExtendedSession;
}

export function DashboardLayoutClient({ children, session }: DashboardLayoutClientProps) {
  const { translate } = useLanguage();
  const pathname = usePathname();
  const user = session.user;

  // Get current role from URL
  const currentRole = getRoleFromPath(pathname);
  const navigationConfig = useNavigationConfig();
  const navigationItems = navigationConfig[currentRole];

  // Onboarding system
  const { shouldShowOnboarding, isNewProvider, completeOnboarding } = useOnboarding();

  // Helper function to render navigation item
  const renderNavigationItem = (item: NavigationItem) => {
    const Icon = item.icon;
    const isActive = isNavigationItemActive(item.href, pathname);

    return (
      <Link
        key={item.href}
        href={item.href}
        onClick={() => trackNavigationClick(item, currentRole)}
        className={cn(
          "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 group",
          isActive
            ? "bg-primary text-primary-foreground shadow-md"
            : "text-muted-foreground hover:text-foreground hover:bg-muted/50 hover:shadow-sm"
        )}
      >
        <Icon className={cn(
          "w-5 h-5 mr-3 transition-transform duration-200",
          isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground group-hover:scale-110"
        )} />
        <span className="truncate">{translate(commonTranslations, item.labelKey)}</span>
        {isActive && (
          <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full opacity-75"></div>
        )}
      </Link>
    );
  };

  const userNameDisplay = user?.name || translate(dashboardLayoutTranslations, 'userPanelTitle');
  const userEmailDisplay = user?.email || "<EMAIL>";



  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Navbar />
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <aside className="hidden md:flex w-80 lg:w-96 shrink-0 border-r bg-card/50">
          <div className="flex flex-col w-full">
            {/* User Profile Section */}
            <div className="p-6 border-b">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground rounded-full w-14 h-14 flex items-center justify-center text-lg font-semibold shadow-md">
                    {userNameDisplay.substring(0,1).toUpperCase()}
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-background rounded-full"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-foreground truncate">{userNameDisplay}</p>
                  <p className="text-sm text-muted-foreground truncate">{userEmailDisplay}</p>
                </div>
              </div>
            </div>

            {/* Navigation Section */}
            <div className="flex-1 overflow-y-auto">
              <nav className="p-4 space-y-6">
                {/* Role indicator section */}
                <div className="space-y-3">
                  <div className="flex items-center px-3 py-2 bg-primary/5 rounded-lg border border-primary/10">
                    {currentRole === 'client' ? (
                      <>
                        <div className="p-1.5 bg-primary/10 rounded-md mr-3">
                          <User className="w-4 h-4 text-primary"/>
                        </div>
                        <span className="text-sm font-medium text-primary">
                          {translate(dashboardLayoutTranslations, 'clientSectionTitle')}
                        </span>
                      </>
                    ) : (
                      <>
                        <div className="p-1.5 bg-primary/10 rounded-md mr-3">
                          <Briefcase className="w-4 h-4 text-primary"/>
                        </div>
                        <span className="text-sm font-medium text-primary">
                          {translate(dashboardLayoutTranslations, 'providerSectionTitle')}
                        </span>
                      </>
                    )}
                  </div>

                  {/* Navigation items */}
                  <div className="space-y-1">
                    {navigationItems.map(renderNavigationItem)}
                  </div>
                </div>
              </nav>
            </div>

            {/* Footer section */}
            <div className="p-4 border-t bg-muted/30">
              <div className="text-xs text-muted-foreground text-center">
                <RoleIndicator currentRole={currentRole} variant="compact" />
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto">
            <div className="max-w-7xl mx-auto p-6 space-y-6">
              {/* New provider welcome banner */}
              {isNewProvider && currentRole === 'provider' && (
                <div className="mb-6">
                  <NewProviderWelcomeBanner />
                </div>
              )}

              {/* Main content area */}
              <div className="space-y-6">
                {children}
              </div>

              {/* Onboarding tooltips */}
              <OnboardingTooltips
                isNewProvider={shouldShowOnboarding}
                onComplete={completeOnboarding}
              />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
