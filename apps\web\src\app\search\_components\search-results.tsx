
"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useLanguage } from '@/contexts/language-context';
import { commonTranslations } from '@repo/translations';
import { SearchFilters } from './filters';
import { CaregiverCard } from '@/components/search/caregiver-card';
import { CaregiverListItem } from '@/components/search/caregiver-list-item';
import { BookingFlow } from '@/components/booking/booking-flow';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON>ead<PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { LayoutGrid, List, ArrowUpDown, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { CaregiverSearchResult } from '@repo/types';

interface SearchResultsProps {
  serviceType: string;
}

const sortOptions = [
  { value: 'relevance', translationKey: 'sortRelevance' },
  { value: 'rating_desc', translationKey: 'sortRatingDesc' },
  { value: 'rating_asc', translationKey: 'sortRatingAsc' },
  { value: 'name_asc', translationKey: 'sortNameAsc' },
  { value: 'name_desc', translationKey: 'sortNameDesc' },
];

export function SearchResults({ serviceType }: SearchResultsProps) {
  const { translate } = useLanguage();
  const { data: session } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [caregivers, setCaregivers] = useState<CaregiverSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [apiError, setApiError] = useState<string | null>(null);
  const [selectedService, setSelectedService] = useState<CaregiverSearchResult | null>(null);
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false);

  const currentPage = useMemo(() => Number(searchParams.get('page') || '1'), [searchParams]);
  const currentSort = useMemo(() => searchParams.get('sort') || 'relevance', [searchParams]);
  const currentUserId = (session?.user as any)?.id;

  const fetchCaregivers = useCallback(async () => {
    setIsLoading(true);
    setApiError(null);
    const params = new URLSearchParams(searchParams.toString());
    params.set('serviceType', serviceType);
    if (currentUserId) {
        params.append('excludeProviderId', String(currentUserId));
    }

    try {
      const response = await fetch(`/api/proxy/services?${params.toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch results.');
      }
      const data = await response.json();
      setCaregivers(data.caregivers || []);
      setTotalPages(data.totalPages || 0);
      setTotalItems(data.totalItems || 0);
    } catch (error) {
      setApiError(error instanceof Error ? error.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, [serviceType, searchParams, currentUserId]);

  useEffect(() => {
    fetchCaregivers();
  }, [fetchCaregivers]);

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('page', String(newPage));
    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  };

  const handleSortChange = (newSort: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('sort', newSort);
    newParams.set('page', '1');
    router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
  };

  const handleBookService = (caregiver: CaregiverSearchResult) => {
    if (!session?.user) {
      router.push('/login');
      return;
    }
    setSelectedService(caregiver);
    setIsBookingDialogOpen(true);
  };

  const handleBookingComplete = (bookingId: number) => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
    router.push('/dashboard/client/bookings');
  };

  const handleBookingCancel = () => {
    setIsBookingDialogOpen(false);
    setSelectedService(null);
  };

  const ResultsSkeleton = () => (
    <div className={cn("gap-6", viewMode === 'grid' ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3" : "space-y-4")}>
      {[...Array(6)].map((_, i) => (
        viewMode === 'grid' ? (
          <Card key={i}><Skeleton className="h-48 w-full" /><CardHeader><Skeleton className="h-6 w-3/4" /></CardHeader><CardContent><Skeleton className="h-4 w-full mb-2" /><Skeleton className="h-4 w-2/3" /></CardContent><CardFooter><Skeleton className="h-10 w-full" /></CardFooter></Card>
        ) : (
          <Card key={i} className="flex"><Skeleton className="h-32 w-32" /><div className="p-4 flex-1 space-y-2"><Skeleton className="h-6 w-3/4" /><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-2/3" /></div></Card>
        )
      ))}
    </div>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div className="md:col-span-1">
        <SearchFilters serviceType={serviceType} />
      </div>
      <div className="md:col-span-3">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
          <p className="text-sm text-muted-foreground">
            {isLoading ? 'Se încarcă...' : `Afișare ${caregivers.length} din ${totalItems} rezultate`}
          </p>
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            <Select value={currentSort} onValueChange={handleSortChange}>
              <SelectTrigger className="w-full sm:w-[180px]"><ArrowUpDown className="w-4 h-4 mr-2 shrink-0" /><SelectValue /></SelectTrigger>
              <SelectContent>
                {sortOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{translate(commonTranslations, opt.translationKey as keyof typeof commonTranslations)}</SelectItem>)}
              </SelectContent>
            </Select>
            <Button variant={viewMode === 'grid' ? 'secondary' : 'outline'} size="icon" onClick={() => setViewMode('grid')}><LayoutGrid /></Button>
            <Button variant={viewMode === 'list' ? 'secondary' : 'outline'} size="icon" onClick={() => setViewMode('list')}><List /></Button>
          </div>
        </div>

        {isLoading ? <ResultsSkeleton /> : apiError ? (
          <Card className="text-center py-12"><CardHeader><AlertTriangle className="mx-auto h-12 w-12 text-destructive" /><CardTitle className="mt-4">Eroare</CardTitle></CardHeader><CardContent><p className="text-muted-foreground">{apiError}</p></CardContent></Card>
        ) : caregivers.length > 0 ? (
          <>
            <div className={cn("gap-6", viewMode === 'grid' ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3" : "space-y-4")}>
              {caregivers.map(c => viewMode === 'grid' ?
                <CaregiverCard key={c.id} caregiver={c} onBookService={() => handleBookService(c)} /> :
                <CaregiverListItem key={c.id} caregiver={c} onBookService={() => handleBookService(c)} />
              )}
            </div>
            {totalPages > 1 && (
              <Pagination className="mt-8">
                <PaginationContent>
                  <PaginationItem><PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} aria-disabled={currentPage === 1} /></PaginationItem>
                  {/* Basic pagination for now */}
                  <PaginationItem><PaginationLink isActive>{currentPage}</PaginationLink></PaginationItem>
                  <PaginationItem><PaginationEllipsis /></PaginationItem>
                  <PaginationItem><PaginationNext onClick={() => handlePageChange(currentPage + 1)} aria-disabled={currentPage >= totalPages} /></PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        ) : (
          <div className="text-center py-12"><h2 className="text-xl font-semibold">Niciun rezultat</h2><p className="text-muted-foreground mt-2">Încearcă să ajustezi filtrele.</p></div>
        )}
      </div>

      {/* Booking Dialog */}
      <Dialog open={isBookingDialogOpen} onOpenChange={setIsBookingDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Rezervă Serviciul</DialogTitle>
            <DialogDescription>
              {selectedService && `Rezervă serviciul "${selectedService.serviceName || selectedService.serviceType}" de la ${selectedService.fullName || selectedService.name}`}
            </DialogDescription>
          </DialogHeader>
          {selectedService && (
            <BookingFlow
              serviceId={selectedService.advertisedServiceId || selectedService.serviceIdForLink}
              providerId={selectedService.id}
              serviceName={selectedService.serviceName || selectedService.serviceType}
              providerName={selectedService.fullName || selectedService.name}
              providerRating={selectedService.averageRating || selectedService.rating}
              servicePrice={selectedService.hourlyRate}
              serviceDuration={selectedService.serviceDuration || 60} // Use service duration or default 1 hour
              serviceIdForLink={selectedService.serviceIdForLink}
              onBookingComplete={handleBookingComplete}
              onCancel={handleBookingCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
